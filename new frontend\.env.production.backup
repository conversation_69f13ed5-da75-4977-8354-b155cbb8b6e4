# ========================================
# GURUKUL FRONTEND - PRODUCTION CONFIGURATION
# ========================================
# This file is auto-generated from Backend/.env
# DO NOT EDIT MANUALLY - Use Backend/.env instead

# Production API Base URLs
VITE_API_BASE_URL=https://api.gurukul.com
VITE_CHAT_API_BASE_URL=https://api.gurukul.com
VITE_FINANCIAL_API_BASE_URL=https://api.gurukul.com
VITE_AGENT_API_BASE_URL=https://api.gurukul.com
VITE_UNIGURU_API_BASE_URL=https://api.gurukul.com

# Supabase Configuration
VITE_SUPABASE_URL=https://qjriwcvexqvqvtegeokv.supabase.co
VITE_SUPABASE_ANON_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************.qsAQ0DfTUwXBZb0BPLWa9XP1mqrhtkjzAxts_l9wyak

# Production Feature Flags
VITE_ENABLE_ANALYTICS=true
VITE_ENABLE_ERROR_REPORTING=true

# AI Provider API Keys (same as development)
VITE_MESHY_API_KEY=
VITE_TRIPO_API_KEY=
VITE_STABILITY_API_KEY=