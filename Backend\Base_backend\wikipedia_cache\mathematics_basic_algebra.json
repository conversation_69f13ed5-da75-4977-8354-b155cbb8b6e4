{"subject": "Mathematics", "topic": "Basic Algebra", "wikipedia": {"title": "Basic Linear Algebra Subprograms", "summary": "Basic Linear Algebra Subprograms (BLAS) is a specification that prescribes a set of low-level routines for performing common linear algebra operations such as vector addition, scalar multiplication, dot products, linear combinations, and matrix multiplication. They are the de facto standard low-level routines for linear algebra libraries; the routines have bindings for both C (\"CBLAS interface\") and Fortran (\"BLAS interface\"). Although the BLAS specification is general, BLAS implementations are often optimized for speed on a particular machine, so using them can bring substantial performance benefits. BLAS implementations will take advantage of special floating point hardware such as vector registers or SIMD instructions.\nIt originated as a Fortran library in 1979 and its interface was standardized by the BLAS Technical (BLAST) Forum, whose latest BLAS report can be found on the netlib website. This Fortran library is known as the reference implementation (sometimes confusingly referred to as the BLAS library) and is not optimized for speed but is in the public domain.\nMost libraries that offer linear algebra routines conform to the BLAS interface, allowing library users to develop programs that are indifferent to the BLAS library being used.\nMany BLAS libraries have been developed, targeting various different hardware platforms. Examples includes cuBLAS (NVIDIA GPU, GPGPU), rocBLAS (AMD GPU), and OpenBLAS. Examples of CPU-based BLAS library branches include: OpenBLAS, BLIS (BLAS-like Library Instantiation Software), Arm Performance Libraries, ATLAS, and Intel Math Kernel Library (iMKL). AMD maintains a fork of BLIS that is optimized for the AMD platform. ATLAS is a portable library that automatically optimizes itself for an arbitrary architecture. iMKL is a freeware and proprietary vendor library optimized for x86 and x86-64 with a performance emphasis on Intel processors. OpenBLAS is an open-source library that is hand-optimized for many of the popular architectures. The LINPACK benchmarks rely heavily on the BLAS routine gemm for its performance measurements.\nMany numerical software applications use BLAS-compatible libraries to do linear algebra computations, including LAPACK, LINPACK, Armadillo, GNU Octave, Mathematica, MATLAB, NumPy, R, Julia and Lisp-Stat.\n\n", "content": "Basic Linear Algebra Subprograms (BLAS) is a specification that prescribes a set of low-level routines for performing common linear algebra operations such as vector addition, scalar multiplication, dot products, linear combinations, and matrix multiplication. They are the de facto standard low-level routines for linear algebra libraries; the routines have bindings for both C (\"CBLAS interface\") and Fortran (\"BLAS interface\"). Although the BLAS specification is general, BLAS implementations are often optimized for speed on a particular machine, so using them can bring substantial performance benefits. BLAS implementations will take advantage of special floating point hardware such as vector registers or SIMD instructions.\nIt originated as a Fortran library in 1979 and its interface was standardized by the BLAS Technical (BLAST) Forum, whose latest BLAS report can be found on the netlib website. This Fortran library is known as the reference implementation (sometimes confusingly referred to as the BLAS library) and is not optimized for speed but is in the public domain.\nMost libraries that offer linear algebra routines conform to the BLAS interface, allowing library users to develop programs that are indifferent to the BLAS library being used.\nMany BLAS libraries have been developed, targeting various different hardware platforms. Examples includes cuBLAS (NVIDIA GPU, GPGPU), rocBLAS (AMD GPU), and OpenBLAS. Examples of CPU-based BLAS library branches include: OpenBLAS, BLIS (BLAS-like Library Instantiation Software), Arm Performance Libraries, ATLAS, and Intel Math Kernel Library (iMKL). AMD maintains a fork of BLIS that is optimized for the AMD platform. ATLAS is a portable library that automatically optimizes itself for an arbitrary architecture. iMKL is a freeware and proprietary vendor library optimized for x86 and x86-64 with a performance emphasis on Intel processors. OpenBLAS is an open-source library that is hand-optimized for many of the popular architectures. The LINPACK benchmarks rely heavily on the BLAS routine gemm for its performance measurements.\nMany numerical software applications use BLAS-compatible libraries to do linear algebra computations, including LAPACK, LINPACK, Armadillo, GNU Octave, Mathematica, MATLAB, NumPy, R, Julia and Lisp-Stat.\n\n\n== Background ==\nWith the advent of numerical programming, sophisticated subroutine libraries became useful.  These libraries would contain subroutines for common high-level mathematical operations such as root finding, matrix inversion, and solving systems of equations. The language of choice was FORTRAN. The most prominent numerical programming library was IBM's Scientific Subroutine Package (SSP).  These subroutine libraries allowed programmers to concentrate on their specific problems and avoid re-implementing well-known algorithms.  The library routines would also be better than average implementations; matrix algorithms, for example, might use full pivoting to get better numerical accuracy. The library routines would also have more efficient routines. For example, a library may include a program to solve a matrix that is upper triangular. The libraries would include single-precision and double-precision versions of some algorithms.\nInitially, these subroutines used hard-coded loops for their low-level operations. For example, if a subroutine needed to perform a matrix multiplication, then the subroutine would have three nested loops. Linear algebra programs have many common low-level operations (the so-called \"kernel\" operations, not related to operating systems). Between 1973 and 1977, several of these kernel operations were identified.  These kernel operations became defined subroutines that math libraries could call.  The kernel calls had advantages over hard-coded loops: the library routine would be more readable, there were fewer chances for bugs, and the kernel implementation could be optimized for speed.  A specification for these kernel operations using scalars and vectors, the level-1 Basic Linear Algebra Subroutines (BLAS), was published in 1979.  BLAS was used to implement the linear algebra subroutine library LINPACK.\nThe BLAS abstraction allows customization for high performance. For example, LINPACK is a general purpose library that can be used on many different machines without modification. LINPACK could use a generic version of BLAS. To gain performance, different machines might use tailored versions of BLAS. As computer architectures became more sophisticated, vector machines appeared. BLAS for a vector machine could use the machine's fast vector operations.  (While vector processors eventually fell out of favor, vector instructions in modern CPUs are essential for optimal performance in BLAS routines.)\nOther machine features became available and could also be exploited.  Consequently, BLAS was augmented from 1984 to 1986 with level-2 kernel operations that concerned vector-matrix operations.  Memory hierarchy was also recognized as something to exploit. Many computers have cache memory that is much faster than main memory; keeping matrix manipulations localized allows better usage of the cache. In 1987 and 1988, the level 3 BLAS were identified to do matrix-matrix operations. The level 3 BLAS encouraged block-partitioned algorithms. The LAPACK library uses level 3 BLAS.\nThe original BLAS concerned only densely stored vectors and matrices. Further extensions to BLAS, such as for sparse matrices, have been addressed.\n\n\n== Functionality ==\nBLAS functionality is categorized into three sets of routines called \"levels\", which correspond to both the chronological order of definition and publication, as well as the degree of the polynomial in the complexities of algorithms; Level 1 BLAS operations typically take linear time, O(n), Level 2 operations quadratic time and Level 3 operations cubic time. Modern BLAS implementations typically provide all three levels.\n\n\n=== Level 1 ===\nThis level consists of all the routines described in the original presentation of BLAS (1979), which defined only vector operations on strided arrays: dot products, vector norms, a generalized vector addition of the form\n\n  \n    \n      \n        \n          y\n        \n        ←\n        α\n        \n          x\n        \n        +\n        \n          y\n        \n      \n    \n    {\\displaystyle {\\boldsymbol {y}}\\leftarrow \\alpha {\\boldsymbol {x}}+{\\boldsymbol {y}}}\n  \n\n(called \"axpy\", \"a x plus y\") and several other operations.\n\n\n=== Level 2 ===\nThis level contains matrix-vector operations including, among other things, a generalized matrix-vector multiplication (gemv):\n\n  \n    \n      \n        \n          y\n        \n        ←\n        α\n        \n          A\n        \n        \n          x\n        \n        +\n        β\n        \n          y\n        \n      \n    \n    {\\displaystyle {\\boldsymbol {y}}\\leftarrow \\alpha {\\boldsymbol {A}}{\\boldsymbol {x}}+\\beta {\\boldsymbol {y}}}\n  \n\nas well as a solver for x in the linear equation\n\n  \n    \n      \n        \n          T\n        \n        \n          x\n        \n        =\n        \n          y\n        \n      \n    \n    {\\displaystyle {\\boldsymbol {T}}{\\boldsymbol {x}}={\\boldsymbol {y}}}\n  \n\nwith T being triangular. Design of the Level 2 BLAS started in 1984, with results published in 1988. The Level 2 subroutines are especially intended to improve performance of programs using BLAS on vector processors, where Level 1 BLAS are suboptimal \"because they hide the matrix-vector nature of the operations from the compiler.\"\n\n\n=== Level 3 ===\nThis level, formally published in 1990, contains matrix-matrix operations, including a \"general matrix multiplication\" (gemm), of the form\n\n  \n    \n      \n        \n          C\n        \n        ←\n        α\n        \n          A\n        \n        \n          B\n        \n        +\n        β\n        \n          C\n        \n        ,\n      \n    \n    {\\displaystyle {\\boldsymbol {C}}\\leftarrow \\alpha {\\boldsymbol {A}}{\\boldsymbol {B}}+\\beta {\\boldsymbol {C}},}\n  \n\nwhere A and B can optionally be transposed or hermitian-conjugated inside the routine, and all three matrices may be strided. The ordinary matrix multiplication A B can be performed by setting α to one and C to an all-zeros matrix of the appropriate size.\nAlso included in Level 3 are routines for computing\n\n  \n    \n      \n        \n          B\n        \n        ←\n        α\n        \n          \n            T\n          \n          \n            −\n            1\n          \n        \n        \n          B\n        \n        ,\n      \n    \n    {\\displaystyle {\\boldsymbol {B}}\\leftarrow \\alpha {\\boldsymbol {T}}^{-1}{\\boldsymbol {B}},}\n  \n\nwhere T is a triangular matrix, among other functionality.\nDue to the ubiquity of matrix multiplications in many scientific applications, including for the implementation of the rest of Level 3 BLAS, and because faster algorithms exist beyond the obvious repetition of matrix-vector multiplication, gemm is a prime target of optimization for BLAS implementers. E.g., by decomposing one or both of A, B into block matrices, gemm can be implemented recursively. This is one of the motivations for including the β parameter, so the results of previous blocks can be accumulated. Note that this decomposition requires the special case β = 1 which many implementations optimize for, thereby eliminating one multiplication for each value of C. This decomposition allows for better locality of reference both in space and time of the data used in the product. This, in turn, takes advantage of the cache on the system. For systems with more than one level of cache, the blocking can be applied a second time to the order in which the blocks are used in the computation. Both of these levels of optimization are used in implementations such as ATLAS. More recently, implementations by Kazushige Goto have shown that blocking only for the L2 cache, combined with careful amortizing of copying to contiguous memory to reduce TLB misses, is superior to ATLAS. A highly tuned implementation based on these ideas is part of the GotoBLAS, OpenBLAS and BLIS.\nA common variation of gemm is the gemm3m, which calculates a complex product using \"three real matrix multiplications and five real matrix additions instead of the conventional four real matrix multiplications and two real matrix additions\", an algorithm similar to Strassen algorithm first described by Peter Ungar.\n\n\n== Implementations ==\nAccelerate\nApple's framework for macOS and iOS, which includes tuned versions of BLAS and LAPACK.\nArm Performance Libraries\nArm Performance Libraries, supporting Arm 64-bit AArch64-based processors, available from Arm.\nATLAS\nAutomatically Tuned Linear Algebra Software, an open source implementation of BLAS APIs for C and Fortran 77.\nBLIS\nBLAS-like Library Instantiation Software framework for rapid instantiation. Optimized for most modern CPUs. BLIS is a complete refactoring of the GotoBLAS that reduces the amount of code that must be written for a given platform.\nC++ AMP BLAS\nThe C++ AMP BLAS Library is an open source implementation of BLAS for Microsoft's AMP language extension for Visual C++.\ncuBLAS\nOptimized BLAS for Nvidia-based GPU cards, requiring few additional library calls.\nNVBLAS\nOptimized BLAS for Nvidia-based GPU cards, providing only Level 3 functions, but as direct drop-in replacement for other BLAS libraries.\nclBLAS\nAn OpenCL implementation of BLAS by AMD. Part of the AMD Compute Libraries.\nclBLAST\nA tuned OpenCL implementation of most of the BLAS api.\nEigen BLAS\nA Fortran 77 and C BLAS library implemented on top of the MPL-licensed Eigen library, supporting x86, x86-64, ARM (NEON), and PowerPC architectures.\nESSL\nIBM's Engineering and Scientific Subroutine Library, supporting the PowerPC architecture under AIX and Linux.\nGotoBLAS\nKazushige Goto's BSD-licensed implementation of BLAS, tuned in particular for Intel Nehalem/Atom, VIA Nanoprocessor, AMD Opteron.\nGNU Scientific Library\nMulti-platform implementation of many numerical routines. Contains a CBLAS interface.\nHP MLIB\nHP's Math library supporting IA-64, PA-RISC, x86 and Opteron architecture under HP-UX and Linux.\nIntel MKL\nThe Intel Math Kernel Library, supporting x86 32-bits and 64-bits, available free from Intel. Includes optimizations for Intel Pentium, Core and Intel Xeon CPUs and Intel Xeon Phi; support for Linux, Windows and macOS.\nMathKeisan\nNEC's math library, supporting NEC SX architecture under SUPER-UX, and Itanium under Linux\nNetlib BLAS\nThe official reference implementation on Netlib, written in Fortran 77.\nNetlib CBLAS\nReference C interface to the BLAS. It is also possible (and popular) to call the Fortran BLAS from C.\nOpenBLAS\nOptimized BLAS based on GotoBLAS, supporting x86, x86-64, MIPS and ARM processors.\nPDLIB/SX\nNEC's Public Domain Mathematical Library for the NEC SX-4 system.\nrocBLAS\nImplementation that runs on AMD GPUs via ROCm.\nSCSL\nSGI's Scientific Computing Software Library contains BLAS and LAPACK implementations for SGI's Irix workstations.\nSun Performance Library\nOptimized BLAS and LAPACK for SPARC, Core and AMD64 architectures under Solaris 8, 9, and 10 as well as Linux.\nuBLAS\nA generic C++ template class library providing BLAS functionality. Part of the Boost library. It provides bindings to many hardware-accelerated libraries in a unifying notation. Moreover, uBLAS focuses on correctness of the algorithms using advanced C++ features.\n\n\n=== Libraries using BLAS ===\nArmadillo\nArmadillo is a C++ linear algebra library aiming towards a good balance between speed and ease of use. It employs template classes, and has optional links to BLAS/ATLAS and LAPACK. It is sponsored by NICTA (in Australia) and is licensed under a free license.\nLAPACK\nLAPACK is a higher level Linear Algebra library built upon BLAS. Like BLAS, a reference implementation exists, but many alternatives like libFlame and MKL exist.\nMir\nAn LLVM-accelerated generic numerical library for science and machine learning written in D. It provides generic linear algebra subprograms (GLAS). It can be built on a CBLAS implementation.\n\n\n== Similar libraries (not compatible with BLAS) ==\n\nElemental\nElemental is an open source software for distributed-memory dense and sparse-direct linear algebra and optimization.\nHASEM\nis a C++ template library, being able to solve linear equations and to compute eigenvalues.  It is licensed under BSD License.\nLAMA\nThe Library for Accelerated Math Applications (LAMA) is a C++ template library for writing numerical solvers targeting various kinds of hardware (e.g. GPUs through CUDA or OpenCL) on distributed memory systems, hiding the hardware specific programming from the program developer\nMTL4\nThe Matrix Template Library version 4 is a generic C++ template library providing sparse and dense BLAS functionality. MTL4 establishes an intuitive interface (similar to MATLAB) and broad applicability thanks to generic programming.\n\n\n== Sparse BLAS ==\nSeveral extensions to BLAS for handling sparse matrices have been suggested over the course of the library's history; a small set of sparse matrix kernel routines was finally standardized in 2002.\n\n\n== Batched BLAS ==\nThe traditional BLAS functions have been also ported to architectures that support large amounts of parallelism such as GPUs. Here, the traditional BLAS functions provide typically good performance for large matrices. However, when computing e.g., matrix-matrix-products of many small matrices by using the GEMM routine, those architectures show significant performance losses. To address this issue, in 2017 a batched version of the BLAS function has been specified.\nTaking the GEMM routine from above as an example, the batched version performs the following computation simultaneously for many matrices:\n\n  \n    \n      \n        \n          C\n        \n        [\n        k\n        ]\n        ←\n        α\n        \n          A\n        \n        [\n        k\n        ]\n        \n          B\n        \n        [\n        k\n        ]\n        +\n        β\n        \n          C\n        \n        [\n        k\n        ]\n        \n        ∀\n        k\n      \n    \n    {\\displaystyle {\\boldsymbol {C}}[k]\\leftarrow \\alpha {\\boldsymbol {A}}[k]{\\boldsymbol {B}}[k]+\\beta {\\boldsymbol {C}}[k]\\quad \\forall k}\n  \n\nThe index \n  \n    \n      \n        k\n      \n    \n    {\\displaystyle k}\n  \n in square brackets indicates that the operation is performed for all matrices \n  \n    \n      \n        k\n      \n    \n    {\\displaystyle k}\n  \n in a stack. Often, this operation is implemented for a strided batched memory layout where all matrices follow concatenated in the arrays \n  \n    \n      \n        A\n      \n    \n    {\\displaystyle A}\n  \n, \n  \n    \n      \n        B\n      \n    \n    {\\displaystyle B}\n  \n and \n  \n    \n      \n        C\n      \n    \n    {\\displaystyle C}\n  \n.\nBatched BLAS functions can be a versatile tool and allow e.g. a fast implementation of exponential integrators and Magnus integrators that handle long integration periods with many time steps. Here, the matrix exponentiation, the computationally expensive part of the integration, can be implemented in parallel for all time-steps by using Batched BLAS functions.\n\n\n== See also ==\nList of numerical libraries\nMath Kernel Library, math library optimized for the Intel architecture; includes BLAS, LAPACK\nNumerical linear algebra, the type of problem BLAS solves\n\n\n== References ==\n\n\n== Further reading ==\nBLAST Forum (2001-08-21), Basic Linear Algebra Subprograms Technical (BLAST) Forum Standard, Knoxville, TN: University of Tennessee\nDodson, D. S.; Grimes, R. G. (1982), \"Remark on algorithm 539: Basic Linear Algebra Subprograms for Fortran usage\", ACM Trans. Math. Softw., 8 (4): 403–404, doi:10.1145/356012.356020, S2CID 43081631\nDodson, D. S. (1983), \"Corrigendum: Remark on \"Algorithm 539: Basic Linear Algebra Subroutines for FORTRAN usage\"\", ACM Trans. Math. Softw., 9: 140, doi:10.1145/356022.356032, S2CID 22163977\nJ. J. Dongarra, J. Du Croz, S. Hammarling, and R. J. Hanson, Algorithm 656: An extended set of FORTRAN Basic Linear Algebra Subprograms, ACM Trans. Math. Softw., 14 (1988), pp. 18–32.\nJ. J. Dongarra, J. Du Croz, I. S. Duff, and S. Hammarling, A set of Level 3 Basic Linear Algebra Subprograms, ACM Trans. Math. Softw., 16 (1990), pp. 1–17.\nJ. J. Dongarra, J. Du Croz, I. S. Duff, and S. Hammarling, Algorithm 679: A set of Level 3 Basic Linear Algebra Subprograms, ACM Trans. Math. Softw., 16 (1990), pp. 18–28.\nNew BLAS\nL. S. Blackford, J. Demmel, J. Dongarra, I. Duff, S. Hammarling, G. Henry, M. Heroux, L. Kaufman, A. Lumsdaine, A. Petitet, R. Pozo, K. Remington, R. C. Whaley, An Updated Set of Basic Linear Algebra Subprograms (BLAS), ACM Trans. Math. Softw., 28-2 (2002), pp. 135–151.\nJ. Dongarra, Basic Linear Algebra Subprograms Technical Forum Standard, International Journal of High Performance Applications and Supercomputing, 16(1) (2002), pp. 1–111, and International Journal of High Performance Applications and Supercomputing, 16(2) (2002), pp. 115–199.\n\n\n== External links ==\nBLAS homepage on Netlib.org\nBLAS FAQ\nBLAS Quick Reference Guide from LAPACK Users' Guide\nLawson Oral History One of the original authors of the BLAS discusses its creation in an oral history interview. Charles L. Lawson Oral history interview by Thomas Haigh, 6 and 7 November 2004, San Clemente, California. Society for Industrial and Applied Mathematics, Philadelphia, PA.\nDongarra Oral History In an oral history interview, Jack Dongarra explores the early relationship of BLAS to LINPACK, the creation of higher level BLAS versions for new architectures, and his later work on the ATLAS system to automatically optimize BLAS for particular machines. Jack Dongarra, Oral history interview by Thomas Haigh, 26 April 2005, University of Tennessee, Knoxville TN. Society for Industrial and Applied Mathematics, Philadelphia, PA\nHow does BLAS get such extreme performance? Ten naive 1000×1000 matrix multiplications (1010 floating point multiply-adds) takes 15.77 seconds on 2.6 GHz processor; BLAS implementation takes 1.32 seconds.\nAn Overview of the Sparse Basic Linear Algebra Subprograms: The New Standard from the BLAS Technical Forum [2]", "url": "https://en.wikipedia.org/wiki/Basic_Linear_Algebra_Subprograms", "related_articles": ["Mathematics", "Algebra", "Undefined (mathematics)", "Linear algebra"]}, "timestamp": 1754462860.6792324}