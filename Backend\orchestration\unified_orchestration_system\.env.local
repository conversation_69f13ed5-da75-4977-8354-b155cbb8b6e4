# Unified Orchestration System Environment Variables

# Primary Gemini API Key
GEMINI_API_KEY=AIzaSyA0S5bqKGucqBfRbdSQkTrJJ7MRsgl7L_A

# Backup Gemini API Key (for failover)
GEMINI_API_KEY_BACKUP=AIzaSyAycjWjrpcYlYsvgSOjEZBfmCFf1WRODn4

# Groq API Key (for alternative LLM support)
GROQ_API_KEY=********************************************************

# System Configuration
LOG_LEVEL=INFO
MAX_RETRIES=3
TIMEOUT_SECONDS=30

# Vector Store Configuration
CHUNK_SIZE=500
CHUNK_OVERLAP=100
MAX_DOCUMENTS_PER_QUERY=5

# Trigger Thresholds
LOW_QUIZ_SCORE_THRESHOLD=60
WELLNESS_CONCERN_THRESHOLD=3
INACTIVITY_DAYS_THRESHOLD=7
REPEATED_QUERIES_THRESHOLD=3

# Sub-agent URLs (if running separately)
TUTORBOT_URL=http://localhost:8001
EMOTIONAL_WELLNESS_BOT_URL=http://localhost:8002
FINANCIAL_WELLNESS_BOT_URL=http://localhost:8003
QUIZBOT_URL=http://localhost:8004
