{"subject": "maths", "topic": "algebra", "wikipedia": {"title": "SageMath", "summary": "SageMath (previously Sage or SAGE, \"System for Algebra and Geometry Experimentation\") is a computer algebra system (CAS) with features covering many aspects of mathematics, including algebra, combinatorics, graph theory, group theory, differentiable manifolds,  numerical analysis, number theory, calculus, and statistics.\nThe first version of SageMath was released on 24 February 2005 as free and open-source software under the terms of the GNU General Public License version 2, with the initial goals of creating an \"open source alternative to Magma, Maple, Mathematica, and MATLAB\". The originator and leader of the SageMath project, <PERSON>, was a mathematician at the University of Washington.\nSageMath uses a syntax resembling Python's, supporting procedural, functional, and object-oriented constructs.", "content": "SageMath (previously Sage or SAGE, \"System for Algebra and Geometry Experimentation\") is a computer algebra system (CAS) with features covering many aspects of mathematics, including algebra, combinatorics, graph theory, group theory, differentiable manifolds,  numerical analysis, number theory, calculus, and statistics.\nThe first version of SageMath was released on 24 February 2005 as free and open-source software under the terms of the GNU General Public License version 2, with the initial goals of creating an \"open source alternative to Magma, Maple, Mathematica, and MATLAB\". The originator and leader of the SageMath project, <PERSON>, was a mathematician at the University of Washington.\nSageMath uses a syntax resembling Python's, supporting procedural, functional, and object-oriented constructs.\n\n\n== Development ==\n\n<PERSON> realized when designing Sage that there were many open-source mathematics software packages already written in different languages, namely C, C++, Common Lisp, Fortran and Python.\nRather than reinventing the wheel, <PERSON> (which is written mostly in Python and Cython) integrates many specialized CAS software packages into a common interface, for which a user needs to know only Python. However, Sage contains hundreds of thousands of unique lines of code adding new functions and creating the interfaces among its components.\nSageMath uses both students and professionals for development. The development of SageMath is supported by both volunteer work and grants. However, it was not until 2016 that the first full-time Sage developer was hired (funded by an EU grant). The same year, <PERSON> described his disappointment with a lack of academic funding and credentials for software development, citing it as the reason for his decision to leave his tenured academic position to work full-time on the project in a newly founded company, SageMath Inc.\n\n\n== Achievements ==\n2007: first prize in the scientific software division of Les Trophées du Libre, an international competition for free software.\n2012: one of the projects selected for the Google Summer of Code.\n2013: ACM/SIGSAM Jenks Prize.\n\n\n== Performance ==\nBoth binaries and source code are available for SageMath from the download page. If SageMath is built from source code, many of the included libraries such as OpenBLAS,  FLINT, GAP (computer algebra system), and NTL will be tuned and optimized for that computer, taking into account the number of processors, the size of their caches, whether there is hardware support for SSE instructions, etc.\nCython can increase the speed of SageMath programs, as the Python code is converted into C.\n\n\n== Licensing and availability ==\nSageMath is free software, distributed under the terms of the GNU General Public License version 3.\n\nWindows\nSageMath 10.0 (May 2023) requires Windows Subsystem for Linux in version 2, which in turn requires Windows to run as a Hyper-V client. SageMath 8.0 (July 2017), with development funded by the OpenDreamKit project, successfully built on Cygwin, and a binary installer for 64-bit versions of Windows was available. Although Microsoft was sponsoring a Windows version of SageMath, prior to 2016 users of Windows had to use virtualization technology such as VirtualBox to run SageMath. \n\nLinux\nSageMath is available as a package in some Linux distributions, including Arch Linux, Debian, Guix, Ubuntu and NixOS. In Gentoo, it is available via layman in the \"sage-on-gentoo\" overlay. The packages used by NixOS and Gnu Guix are available for use on other distributions, due to the distribution-agnostic nature of their package managers.\n\nOther operating systems\nGentoo prefix also provides Sage on other operating systems.\n\n\n== Software packages contained in SageMath ==\nThe philosophy of SageMath is to use existing open-source libraries wherever they exist. Therefore, it uses many libraries from other projects.\n\n\n== See also ==\n\nCoCalc\nComparison of numerical-analysis software\nComparison of statistical packages\nList of computer algebra systems\n\n\n== References ==\n\n\n== External links ==\n\nOfficial website", "url": "https://en.wikipedia.org/wiki/SageMath", "related_articles": ["<PERSON> (mathematics)", "Computer algebra", "Linear algebra", "Equation"]}, "timestamp": 1754298586.5146773}