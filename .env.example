# Production Environment Configuration Template
# Copy this file to .env and Backend/.env and fill in your actual values

# Database Configuration
MONGO_ROOT_USERNAME=admin
MONGO_ROOT_PASSWORD=your_secure_mongodb_password
MONGODB_URL=***********************************************************************************

# Redis Configuration
REDIS_PASSWORD=your_secure_redis_password
REDIS_HOST=redis
REDIS_PORT=6379

# API Keys - Replace with your actual keys
GROQ_API_KEY=your_groq_api_key_here
OPENAI_API_KEY=your_openai_api_key_here
HUGGINGFACE_API_KEY=your_huggingface_api_key_here
ANTHROPIC_API_KEY=your_anthropic_api_key_here

# Service URLs (for production, use your domain)
BASE_BACKEND_URL=http://localhost:8000
CHATBOT_API_URL=http://localhost:8001
FINANCIAL_API_URL=http://localhost:8002
MEMORY_API_URL=http://localhost:8003
AKASH_API_URL=http://localhost:8004
SUBJECT_API_URL=http://localhost:8005
KARTHIKEYA_API_URL=http://localhost:8006
TTS_API_URL=http://localhost:8007

# Frontend Environment Variables
VITE_BASE_API_URL=http://localhost:8000
VITE_CHAT_API_URL=http://localhost:8001
VITE_FINANCIAL_API_URL=http://localhost:8002
VITE_MEMORY_API_URL=http://localhost:8003
VITE_AKASH_API_URL=http://localhost:8004
VITE_SUBJECT_API_URL=http://localhost:8005
VITE_KARTHIKEYA_API_URL=http://localhost:8006
VITE_TTS_API_URL=http://localhost:8007

# Security Settings
JWT_SECRET_KEY=your_jwt_secret_key_here
ENCRYPTION_KEY=your_encryption_key_here

# Application Settings
DEBUG=false
LOG_LEVEL=INFO
ENVIRONMENT=production

# TTS Configuration
TTS_ENABLED=true
TTS_MODEL=tts-1
TTS_VOICE=alloy

# File Upload Settings
MAX_FILE_SIZE=50MB
UPLOAD_PATH=/app/uploads

# CORS Settings
ALLOWED_ORIGINS=http://localhost:3000,https://yourdomain.com
ALLOWED_METHODS=GET,POST,PUT,DELETE,OPTIONS
ALLOWED_HEADERS=*

# Rate Limiting
RATE_LIMIT_REQUESTS=100
RATE_LIMIT_WINDOW=60

# Session Configuration
SESSION_TIMEOUT=3600
SESSION_SECRET=your_session_secret_here
