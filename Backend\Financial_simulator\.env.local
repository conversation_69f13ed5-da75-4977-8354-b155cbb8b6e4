# MongoDB Configuration
MONGODB_URI=mongodb+srv://blackhole_user:<EMAIL>/?retryWrites=true&w=majority&appName=user

# OpenAI API Configuration blackholeinfiverse1
# OPENAI_API_KEY=your_openai_api_key_here

# Groq API Configuration (Alternative to OpenAI)
GROQ_API_KEY=********************************************************

# Redis Configuration (Optional - will use memory fallback if not available)
# REDIS_HOST=localhost
# REDIS_PORT=6379
# REDIS_PASSWORD=

# AgentOps Configuration (Already configured in code)
AGENTOPS_API_KEY=4be58a32-e415-4142-82b7-834ae6b95422

# Server Configuration
HOST=0.0.0.0
PORT=8002

# Application Settings
DEBUG=true
ENVIRONMENT=development
